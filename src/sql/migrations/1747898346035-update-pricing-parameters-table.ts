import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdatePricingParametersTable1747898346035 implements MigrationInterface {
  name = 'UpdatePricingParametersTable1747898346035'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP CONSTRAINT "FK_14049d637e433b997d7f8924dce"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP CONSTRAINT "XCL_14d557ed6f6615220b880770e4"`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" RENAME COLUMN "name" TO "type"`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" RENAME CONSTRAINT "UQ_b175526852708330021452f358c" TO "UQ_aaf656b05aa4a80550bac0746f8"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "contract_type_uuid"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "kilometer_rate"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "minute_rate"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "weekend_markup"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "unloaded_kilometer_factor"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "unloaded_minute_factor"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "flexibility_factor"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "pricing_formula_uuid" uuid NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "extra_minutes_per_seat_taken" integer`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "extra_minutes_per_wheelchair" integer`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" DROP CONSTRAINT "UQ_aaf656b05aa4a80550bac0746f8"`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" DROP COLUMN "type"`)
    await queryRunner.query(`CREATE TYPE "public"."pricing_formula_type" AS ENUM('hendriks_formula', 'reva_flanders_formula', 'reva_federal_formula', 'nmbs_matrix_formula', 'fixed_price')`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" ADD "type" "public"."pricing_formula_type" NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ALTER COLUMN "base_charge" DROP NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD CONSTRAINT "XCL_b4d5ea3e6ac29eb26f17c1e703" EXCLUDE USING GIST (pricing_formula_uuid WITH =, effective_period WITH &&) WHERE (deleted_at IS NULL)`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD CONSTRAINT "FK_8bed93db5d00cf4e3354885a001" FOREIGN KEY ("pricing_formula_uuid") REFERENCES "pricing_formula"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP CONSTRAINT "FK_8bed93db5d00cf4e3354885a001"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP CONSTRAINT "XCL_b4d5ea3e6ac29eb26f17c1e703"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ALTER COLUMN "base_charge" SET NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" DROP COLUMN "type"`)
    await queryRunner.query(`DROP TYPE "public"."pricing_formula_type"`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" ADD "type" character varying NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" ADD CONSTRAINT "UQ_aaf656b05aa4a80550bac0746f8" UNIQUE ("type")`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "extra_minutes_per_wheelchair"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "extra_minutes_per_seat_taken"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "pricing_formula_uuid"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "flexibility_factor" jsonb NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "unloaded_minute_factor" jsonb NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "unloaded_kilometer_factor" jsonb NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "weekend_markup" numeric(4,0) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "minute_rate" integer NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "kilometer_rate" integer NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "contract_type_uuid" uuid NOT NULL`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" RENAME CONSTRAINT "UQ_aaf656b05aa4a80550bac0746f8" TO "UQ_b175526852708330021452f358c"`)
    await queryRunner.query(`ALTER TABLE "pricing_formula" RENAME COLUMN "type" TO "name"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD CONSTRAINT "XCL_14d557ed6f6615220b880770e4" EXCLUDE USING gist (contract_type_uuid WITH =, effective_period WITH &&) WHERE ((deleted_at IS NULL))`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD CONSTRAINT "FK_14049d637e433b997d7f8924dce" FOREIGN KEY ("contract_type_uuid") REFERENCES "contract_type"("uuid") ON DELETE NO ACTION ON UPDATE NO ACTION`)
  }
}
