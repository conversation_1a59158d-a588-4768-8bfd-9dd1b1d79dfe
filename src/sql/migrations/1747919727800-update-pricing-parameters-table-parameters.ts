import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdatePricingParametersTableParameters1747919727800 implements MigrationInterface {
  name = 'UpdatePricingParametersTableParameters1747919727800'

  public async up (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "extra_minutes_per_seat_taken"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "extra_minutes_per_seat_taken" float`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "extra_minutes_per_wheelchair"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "extra_minutes_per_wheelchair" float`)
  }

  public async down (queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "extra_minutes_per_wheelchair"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "extra_minutes_per_wheelchair" integer`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" DROP COLUMN "extra_minutes_per_seat_taken"`)
    await queryRunner.query(`ALTER TABLE "pricing_parameters" ADD "extra_minutes_per_seat_taken" integer`)
  }
}
