import assert from 'assert'
import { Currency, IsMonetary, Monetary, MonetaryAmountColumn, MonetaryAmountColumnOptions, MonetaryDto } from '@wisemen/monetary'
import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger'
import { ValidationApiError } from '../../modules/exceptions/api-errors/validation.api-error.js'

export const EURO_MAX_PRECISION = 4

export class Euros extends Monetary {
  constructor (dto: MonetaryDto)
  constructor (amount: number)
  constructor (amount: number, precision: number)
  constructor (amountOrDto: number | MonetaryDto, precision = EURO_MAX_PRECISION) {
    if (typeof amountOrDto === 'object') {
      assert(amountOrDto.currency === Currency.EUR, new ValidationApiError('invalid currency'))
      super(amountOrDto.amount, Currency.EUR, amountOrDto.precision)
    } else {
      super(amountOrDto, Currency.EUR, precision)
    }
  }
}

export function EurosApiProperty (options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    type: MonetaryDto,
    description: `only accepts currency ${Currency.EUR} and max precision ${EURO_MAX_PRECISION}`,
    ...options
  })
}

export function IsEuros (): PropertyDecorator {
  const allowedCurrencies = new Set<Currency>([Currency.EUR])
  return IsMonetary({ allowedCurrencies, maxPrecision: EURO_MAX_PRECISION })
}

export function EurosColumn (options?: Omit<MonetaryAmountColumnOptions, 'monetaryPrecision' | 'currency'>): PropertyDecorator {
  return MonetaryAmountColumn({
    ...options,
    currency: Currency.EUR,
    monetaryPrecision: EURO_MAX_PRECISION
  })
}
