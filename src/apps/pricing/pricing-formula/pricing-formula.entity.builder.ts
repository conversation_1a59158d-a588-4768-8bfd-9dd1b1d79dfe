import { randomUUID } from 'crypto'
import { UserUuid } from '../../../app/users/entities/user.uuid.js'
import { PricingFormula } from './entities/pricing-formula.entity.js'
import { PricingFormulaType } from './pricing-formula-types.js'

export class PricingFormulaEntityBuilder {
  private pricingFormula: PricingFormula

  constructor () {
    this.pricingFormula = new PricingFormula()
    this.pricingFormula.uuid = randomUUID()
    this.pricingFormula.createdAt = new Date()
    this.pricingFormula.updatedAt = new Date()
    this.pricingFormula.type = PricingFormulaType.HENDRIKS_FORMULA
    this.pricingFormula.lastUpdatedByUserUuid = randomUUID() as UserUuid
  }

  withUuid (uuid: string): this {
    this.pricingFormula.uuid = uuid
    return this
  }

  withCreatedAt (createdAt: Date): this {
    this.pricingFormula.createdAt = createdAt
    return this
  }

  withUpdatedAt (updatedAt: Date): this {
    this.pricingFormula.updatedAt = updatedAt
    return this
  }

  withType (name: PricingFormulaType): this {
    this.pricingFormula.type = name
    return this
  }

  withLastUpdatedByUserUuid (uuid: UserUuid): this {
    this.pricingFormula.lastUpdatedByUserUuid = uuid
    return this
  }

  build (): PricingFormula {
    return this.pricingFormula
  }
}
