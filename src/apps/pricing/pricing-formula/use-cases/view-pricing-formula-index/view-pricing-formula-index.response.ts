import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { PricingFormula } from '../../entities/pricing-formula.entity.js'
import { PricingFormulaType, PricingFormulaTypeApiProperty } from '../../pricing-formula-types.js'

class PricingFormulaResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @PricingFormulaTypeApiProperty()
  type: PricingFormulaType

  @ApiProperty({ type: Boolean })
  isEditable: boolean

  @ApiProperty({ type: String, format: 'uuid', nullable: true })
  lastUpdatedByUserUuid: string | null

  @ApiProperty({ type: String, example: 'John', nullable: true })
  lastUpdatedByUserName: string | null

  constructor (pricingFormula: PricingFormula) {
    assert(pricingFormula.lastUpdatedByUser !== undefined, 'lastUpdatedByUser is required')

    this.uuid = pricingFormula.uuid
    this.createdAt = pricingFormula.createdAt.toISOString()
    this.updatedAt = pricingFormula.updatedAt.toISOString()
    this.type = pricingFormula.type
    this.isEditable = pricingFormula.isEditable
    this.lastUpdatedByUserUuid = pricingFormula.lastUpdatedByUserUuid
    this.lastUpdatedByUserName = pricingFormula.lastUpdatedByUser?.fullName ?? null
  }
}

export class ViewPricingFormulaIndexResponse
  extends PaginatedOffsetResponse<PricingFormulaResponse> {
  @ApiProperty({ type: PricingFormulaResponse, isArray: true })
  declare items: PricingFormulaResponse[]

  constructor (items: PricingFormula[], total: number, limit: number, offset: number) {
    const result = items.map(pricingFormula => new PricingFormulaResponse(pricingFormula))

    super(result, total, limit, offset)
  }
}
