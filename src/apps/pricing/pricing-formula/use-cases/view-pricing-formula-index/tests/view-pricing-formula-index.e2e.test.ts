import { after, before, describe, it } from 'node:test'
import { expect } from 'expect'
import { stringify } from 'qs'
import request from 'supertest'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { PricingFormula } from '../../../entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../pricing-formula.entity.builder.js'
import { User } from '../../../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../../../app/users/tests/user-entity.builder.js'
import { PricingFormulaType } from '../../../pricing-formula-types.js'
import { ViewPricingFormulaIndexQueryBuilder } from './view-pricing-formula-index.query.builder.js'

describe('view pricing formula index e2e tests', () => {
  let setup: EndToEndTestSetup
  let token: string
  let user: User

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    const adminUser = await setup.authContext.getAdminUser()
    token = adminUser.token

    user = new UserEntityBuilder().build()
    await setup.dataSource.manager.insert(User, user)
  })

  after(async () => await setup.teardown())

  it('returns pricing formulas in a paginated format', async () => {
    const formula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(user.uuid)
      .build()

    await setup.dataSource.manager.insert(PricingFormula, formula)

    const query = new ViewPricingFormulaIndexQueryBuilder()
      .withLimit(10)
      .withOffset(0)
      .build()

    const response = await request(setup.httpServer)
      .get('/pricing-formulas')
      .set('Authorization', `Bearer ${token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
    expect(response.body).toStrictEqual(expect.objectContaining({
      items: expect.arrayContaining([
        expect.objectContaining({
          uuid: formula.uuid,
          type: formula.type,
          lastUpdatedByUserUuid: user.uuid,
          lastUpdatedByUserName: `${user.firstName} ${user.lastName}`
        })
      ]),
      meta: {
        total: expect.any(Number),
        offset: 0,
        limit: 10
      }
    }))
  })
})
