import { Column, CreateDateColumn, Entity, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { UserUuid } from '../../../../app/users/entities/user.uuid.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { PricingFormulaTypeColumn, PricingFormulaType } from '../pricing-formula-types.js'

@Entity()
export class PricingFormula {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @PricingFormulaTypeColumn()
  type: PricingFormulaType

  @Column({ type: 'boolean', default: true })
  isEditable: boolean

  @Column({ type: 'uuid', nullable: true })
  lastUpdatedByUserUuid: UserUuid | null

  @ManyToOne(() => User)
  @JoinColumn({ name: 'last_updated_by_user_uuid' })
  lastUpdatedByUser?: Relation<User> | null
}
