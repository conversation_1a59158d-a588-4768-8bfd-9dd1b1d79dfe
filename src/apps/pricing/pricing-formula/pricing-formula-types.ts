import { ApiPropertyOptions, ApiProperty } from '@nestjs/swagger'
import { Column } from 'typeorm'

export enum PricingFormulaType {
  HENDRIKS_FORMULA = 'hendriks_formula',
  REVA_FLANDERS_FORMULA = 'reva_flanders_formula',
  REVA_FEDERAL_FORMULA = 'reva_federal_formula',
  NMBS_MATRIX_FORMULA = 'nmbs_matrix_formula',
  FIXED_PRICE = 'fixed_price'
}

export function PricingFormulaTypeApiProperty (options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    ...options,
    enum: PricingFormulaType,
    enumName: 'PricingFormulaType'
  })
}

export function PricingFormulaTypeColumn (
  options?: { default: PricingFormulaType }
): PropertyDecorator {
  return Column({
    type: 'enum',
    enum: PricingFormulaType,
    enumName: 'pricing_formula_type',
    ...options
  })
}
