import { Module } from '@nestjs/common'
import { ViewPricingParametersOnDateModule } from './use-cases/view-pricing-parameters-on-date/view-pricing-parameters-on-date.module.js'
import { ViewPricingParametersByFormulaModule } from './use-cases/view-pricing-parameters-ranges/view-pricing-parameters-ranges.module.js'
import { CreatePricingParametersOnDateModule } from './use-cases/create-pricing-parameters-on-date/create-pricing-parameters-on-date.module.js'
import { UpdatePricingParametersModule } from './use-cases/update-pricing-parameters/update-pricing-parameters.module.js'
import { DeletePricingParametersModule } from './use-cases/delete-pricing-parameters/delete-pricing-parameters.module.js'

@Module({
  imports: [
    ViewPricingParametersOnDateModule,
    ViewPricingParametersByFormulaModule,
    CreatePricingParametersOnDateModule,
    UpdatePricingParametersModule,
    DeletePricingParametersModule
  ]
})
export class PricingParametersModule {}
