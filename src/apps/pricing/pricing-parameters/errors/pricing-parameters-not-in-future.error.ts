import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../../../modules/exceptions/api-errors/bad-request.api-error.js'

export class PricingParametersNotInFutureError extends BadRequestApiError {
  @ApiErrorCode('pricing_parameters_not_in_future')
  code = 'pricing_parameters_not_in_future'

  meta: never

  constructor (uuid: string) {
    super(`Pricing parameters with UUID ${uuid} cannot be updated because they are not in the future`)
  }
}
