import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../../../modules/exceptions/api-errors/not-found.api-error.js'

export class NoLeftAdjacentPricingParametersFoundError extends NotFoundApiError {
  @ApiErrorCode('no_left_adjacent_pricing_parameters_found')
  code = 'no_left_adjacent_pricing_parameters_found'

  meta: never

  constructor (pricingFormulaUuid: string) {
    super(`No left adjacent pricing parameters found for pricing formula ${pricingFormulaUuid}`)
  }
}
