import { ApiErrorCode } from '../../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../../../modules/exceptions/api-errors/not-found.api-error.js'

export class PricingParametersNotFoundError extends NotFoundApiError {
  @ApiErrorCode('pricing_parameters_not_found')
  code = 'pricing_parameters_not_found'

  meta: never

  constructor (pricingFormulaUuid: string) {
    super(`Pricing parameters for pricing formula ${pricingFormulaUuid} not found`)
  }
}
