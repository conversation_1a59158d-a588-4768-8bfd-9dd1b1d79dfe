import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DatePeriod } from '../../../../../utils/dates/date-period.enum.js'
import { WiseDate } from '../../../../../utils/dates/wise-date/wise-date.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { PricingParametersNotInFutureError } from '../../errors/pricing-parameters-not-in-future.error.js'
import { PricingParametersNotFoundError } from '../../errors/pricing-parameters-not-found.error.js'
import { UpdatePricingParametersCommand } from './update-pricing-parameters.command.js'
import { UpdatePricingParametersRepository } from './update-pricing-parameters.repository.js'
import { PricingParametersUpdatedEvent } from './pricing-parameters-updated.event.js'

@Injectable()
export class UpdatePricingParametersUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly repository: UpdatePricingParametersRepository
  ) {}

  async execute (
    pricingParametersUuid: string,
    command: UpdatePricingParametersCommand
  ): Promise<void> {
    const pricingParameters = await this.repository.getPricingParameters(pricingParametersUuid)

    if (!pricingParameters) {
      throw new PricingParametersNotFoundError(pricingParametersUuid)
    }

    const datePeriod = pricingParameters.effectivePeriod.getDatePeriod(WiseDate.today())
    if (datePeriod !== DatePeriod.FUTURE) {
      throw new PricingParametersNotInFutureError(pricingParametersUuid)
    }

    const event = new PricingParametersUpdatedEvent(pricingParametersUuid)

    await transaction(this.dataSource, async () => {
      await this.repository.updatePricingParameters(pricingParametersUuid, command)
      await this.eventEmitter.emitOne(event)
    })
  }
}
