import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsPositive } from 'class-validator'
import { MonetaryDto } from '@wisemen/monetary'
import { IsNullable } from '@wisemen/validators'
import { EurosApiProperty, IsEuros } from '../../../../../utils/monetary/euros.js'

export class UpdatePricingParametersCommand {
  @EurosApiProperty({ nullable: true })
  @IsNullable()
  @IsEuros()
  baseCharge: MonetaryDto | null

  @ApiProperty({
    type: Number,
    format: 'float',
    nullable: true,
    description: 'The number of extra minutes per seat taken.'
  })
  @IsNullable()
  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  extraMinutesPerSeatTaken: number | null

  @ApiProperty({
    type: Number,
    format: 'float',
    nullable: true,
    description: 'The number of extra minutes per wheelchair.'
  })
  @IsNullable()
  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  extraMinutesPerWheelchair: number | null
}
