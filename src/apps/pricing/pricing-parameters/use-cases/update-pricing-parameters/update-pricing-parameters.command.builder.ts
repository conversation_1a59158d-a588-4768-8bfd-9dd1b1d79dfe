import { MonetaryDtoBuilder } from '@wisemen/monetary'
import { Currency } from '@wisemen/monetary'
import { UpdatePricingParametersCommand } from './update-pricing-parameters.command.js'

export class UpdatePricingParametersCommandBuilder {
  private readonly command: UpdatePricingParametersCommand

  constructor () {
    this.command = new UpdatePricingParametersCommand()
    this.command.baseCharge = null
    this.command.extraMinutesPerSeatTaken = null
    this.command.extraMinutesPerWheelchair = null
  }

  withBaseCharge (baseCharge: number): this {
    this.command.baseCharge = new MonetaryDtoBuilder()
      .withAmount(baseCharge)
      .withCurrency(Currency.EUR)
      .withPrecision(2)
      .build()
    return this
  }

  withExtraMinutesPerSeatTaken (minutes: number): this {
    this.command.extraMinutesPerSeatTaken = minutes
    return this
  }

  withExtraMinutesPerWheelchair (minutes: number): this {
    this.command.extraMinutesPerWheelchair = minutes
    return this
  }

  build (): UpdatePricingParametersCommand {
    return this.command
  }
}
