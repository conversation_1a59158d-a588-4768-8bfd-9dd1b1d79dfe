import { Body, Controller, Patch } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { PricingParametersNotInFutureError } from '../../errors/pricing-parameters-not-in-future.error.js'
import { PricingParametersNotFoundError } from '../../errors/pricing-parameters-not-found.error.js'
import { UpdatePricingParametersUseCase } from './update-pricing-parameters.use-case.js'
import { UpdatePricingParametersCommand } from './update-pricing-parameters.command.js'

@ApiTags('PricingParameters')
@ApiOAuth2([])
@Controller('pricing-parameters/:pricingParametersUuid')
export class UpdatePricingParametersController {
  constructor (
    private readonly updatePricingParametersUseCase: UpdatePricingParametersUseCase
  ) {}

  @Patch()
  @ApiNotFoundErrorResponse(PricingParametersNotFoundError)
  @ApiBadRequestErrorResponse(PricingParametersNotInFutureError)
  async updatePricingParameters (
    @UuidParam('pricingParametersUuid') pricingParametersUuid: string,
    @Body() command: UpdatePricingParametersCommand
  ): Promise<void> {
    await this.updatePricingParametersUseCase.execute(
      pricingParametersUuid,
      command
    )
  }
}
