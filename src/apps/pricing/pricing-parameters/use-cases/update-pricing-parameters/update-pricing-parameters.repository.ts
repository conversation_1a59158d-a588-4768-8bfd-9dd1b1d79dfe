import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { Euros } from '../../../../../utils/monetary/euros.js'
import { UpdatePricingParametersCommand } from './update-pricing-parameters.command.js'

@Injectable()
export class UpdatePricingParametersRepository {
  constructor (
    @InjectRepository(PricingParameters)
    private readonly pricingParametersRepository: Repository<PricingParameters>
  ) {}

  async getPricingParameters (withUuid: string): Promise<PricingParameters | null> {
    return await this.pricingParametersRepository.findOneBy({ uuid: withUuid })
  }

  async updatePricingParameters (
    uuid: string,
    command: UpdatePricingParametersCommand
  ): Promise<void> {
    await this.pricingParametersRepository.update(uuid, {
      baseCharge: command.baseCharge ? new Euros(command.baseCharge) : null,
      extraMinutesPerSeatTaken: command.extraMinutesPerSeatTaken,
      extraMinutesPerWheelchair: command.extraMinutesPerWheelchair
    })
  }
}
