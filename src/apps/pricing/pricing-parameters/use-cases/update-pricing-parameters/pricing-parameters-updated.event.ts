import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'

@OneOfMeta(DomainEventLog, DomainEventType.PRICING_PARAMETERS_UPDATED)
export class PricingParametersUpdatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly pricingParametersUuid: string

  constructor (pricingParametersUuid: string) {
    this.pricingParametersUuid = pricingParametersUuid
  }
}

@RegisterDomainEvent(DomainEventType.PRICING_PARAMETERS_UPDATED, 1)
export class PricingParametersUpdatedEvent
  extends DomainEvent<PricingParametersUpdatedEventContent> {
  constructor (pricingParametersUuid: string) {
    super({
      content: new PricingParametersUpdatedEventContent(pricingParametersUuid)
    })
  }
}
