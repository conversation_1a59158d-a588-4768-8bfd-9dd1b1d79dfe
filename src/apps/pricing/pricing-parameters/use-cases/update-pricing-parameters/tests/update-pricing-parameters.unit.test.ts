import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { UpdatePricingParametersRepository } from '../update-pricing-parameters.repository.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { PricingParametersNotInFutureError } from '../errors/pricing-parameters-not-in-future.error.js'
import { UpdatePricingParametersCommandBuilder } from '../update-pricing-parameters.command.builder.js'
import { UpdatePricingParametersUseCase } from '../update-pricing-parameters.use-case.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PricingParametersNotFoundError } from '../../view-pricing-parameters-on-date/errors/pricing-parameters-not-found.error.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'

describe('UpdatePricingParametersUseCase', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('should throw an error when the pricing parameters do not exist', async () => {
    const repository = createStubInstance(UpdatePricingParametersRepository)
    repository.getPricingParameters.resolves(null)

    const useCase = new UpdatePricingParametersUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      repository
    )

    const pricingParametersUuid = randomUUID()
    const command = new UpdatePricingParametersCommandBuilder().build()

    await expect(useCase.execute(pricingParametersUuid, command))
      .rejects.toThrow(new PricingParametersNotFoundError(pricingParametersUuid))
  })

  it('should throw an error when the pricing parameters are not in the future', async () => {
    const repository = createStubInstance(UpdatePricingParametersRepository)
    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(
        WiseDate.today().subtract(1, 'day'),
        WiseDate.today().add(1, 'day')
      ))
      .build()
    repository.getPricingParameters.resolves(currentPricingParameters)

    const useCase = new UpdatePricingParametersUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      repository
    )

    const pricingParametersUuid = randomUUID()
    const command = new UpdatePricingParametersCommandBuilder().build()

    await expect(useCase.execute(pricingParametersUuid, command))
      .rejects.toThrow(new PricingParametersNotInFutureError(pricingParametersUuid))
  })

  it('should successfully update future pricing parameters', async () => {
    const repository = createStubInstance(UpdatePricingParametersRepository)

    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(
        WiseDate.today().add(1, 'day'),
        new FutureInfinityDate()
      ))
      .build()

    repository.getPricingParameters.resolves(futurePricingParameters)

    const useCase = new UpdatePricingParametersUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      repository
    )

    const pricingParametersUuid = randomUUID()
    const command = new UpdatePricingParametersCommandBuilder()
      .withBaseCharge(2000)
      .withExtraMinutesPerSeatTaken(5)
      .withExtraMinutesPerWheelchair(10)
      .build()

    await expect(useCase.execute(pricingParametersUuid, command))
      .resolves.not.toThrow()
  })
})
