import { afterEach, beforeEach, describe, it } from 'node:test'
import { HttpStatus } from '@nestjs/common'
import request from 'supertest'
import { expect } from 'expect'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { PricingFormula } from '../../../../pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../pricing-formula/pricing-formula.entity.builder.js'
import { PricingFormulaType } from '../../../../pricing-formula/pricing-formula-types.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { Euros } from '../../../../../../utils/monetary/euros.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PricingParameters } from '../../../pricing-parameters.entity.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { UpdatePricingParametersCommandBuilder } from '../update-pricing-parameters.command.builder.js'

describe('UpdatePricingParametersUseCase E2E', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser

  beforeEach(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
  })

  afterEach(async () => {
    await testSetup.teardown()
  })

  it('successfully updates future pricing parameters', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    const futureEffectivePeriod = new DateRange(
      WiseDate.tomorrow(),
      new FutureInfinityDate()
    )

    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    await testSetup.dataSource.manager.insert(PricingParameters, futurePricingParameters)

    const command = new UpdatePricingParametersCommandBuilder()
      .withBaseCharge(2000)
      .withExtraMinutesPerSeatTaken(5)
      .withExtraMinutesPerWheelchair(10)
      .build()

    const response = await request(testSetup.httpServer)
      .patch(`/pricing-parameters/${futurePricingParameters.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)

    const updatedPricingParameters = await testSetup.dataSource.manager.findOne(PricingParameters, {
      where: { uuid: futurePricingParameters.uuid }
    })

    expect(updatedPricingParameters).not.toBeNull()
    expect(updatedPricingParameters!.baseCharge!.amount).toBe(200000)
    expect(updatedPricingParameters!.extraMinutesPerSeatTaken).toBe(5)
    expect(updatedPricingParameters!.extraMinutesPerWheelchair).toBe(10)
  })
})
