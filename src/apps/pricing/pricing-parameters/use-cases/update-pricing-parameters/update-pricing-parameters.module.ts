import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { UpdatePricingParametersController } from './update-pricing-parameters.controller.js'
import { UpdatePricingParametersUseCase } from './update-pricing-parameters.use-case.js'
import { UpdatePricingParametersRepository } from './update-pricing-parameters.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PricingParameters]),
    DomainEventEmitterModule
  ],
  controllers: [UpdatePricingParametersController],
  providers: [UpdatePricingParametersUseCase, UpdatePricingParametersRepository]
})
export class UpdatePricingParametersModule {}
