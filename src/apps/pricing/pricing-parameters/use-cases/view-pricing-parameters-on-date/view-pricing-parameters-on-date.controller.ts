import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags, ApiNotFoundResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ViewPricingParametersOnDateUseCase } from './view-pricing-parameters-on-date.use-case.js'
import { ViewPricingParametersOnDateResponse } from './view-pricing-parameters-on-date.response.js'
import { ViewPricingParametersOndDateQuery } from './view-pricing-parameters-on-date.query.js'

@ApiTags('PricingParameters')
@ApiOAuth2([])
@Controller('pricing-formulas/:pricingFormulaUuid/pricing-parameters')
export class ViewPricingParametersOnDateController {
  constructor (
    private readonly viewCurrentPricingParametersUseCase: ViewPricingParametersOnDateUseCase
  ) {}

  @Get()
  @ApiOkResponse({
    description: 'The current pricing parameters for the specified pricing formula',
    type: ViewPricingParametersOnDateResponse
  })
  @ApiNotFoundResponse({
    description: 'No pricing parameters found for the specified pricing formula'
  })
  async viewCurrentPricingParameters (
    @UuidParam('pricingFormulaUuid') pricingFormulaUuid: string,
    @Query() query: ViewPricingParametersOndDateQuery
  ): Promise<ViewPricingParametersOnDateResponse> {
    return await this.viewCurrentPricingParametersUseCase.execute(
      pricingFormulaUuid,
      query.filter.date
    )
  }
}
