import { IsObject, IsOptional, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsDateWithoutTimeString } from '@wisemen/validators'

export class ViewPricingParametersOnDateFilterQuery {
  @ApiProperty({ type: String, format: 'date' })
  @IsDateWithoutTimeString()
  date: string
}

export class ViewPricingParametersOndDateQuery {
  @ApiProperty({ type: ViewPricingParametersOnDateFilterQuery, required: false })
  @IsOptional()
  @IsObject()
  @Type(() => ViewPricingParametersOnDateFilterQuery)
  @ValidateNested()
  filter: ViewPricingParametersOnDateFilterQuery
}
