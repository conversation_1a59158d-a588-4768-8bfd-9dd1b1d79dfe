import { ApiProperty } from '@nestjs/swagger'
import { MonetaryDto } from '@wisemen/monetary'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DatePeriodApiProperty, DatePeriod } from '../../../../../utils/dates/date-period.enum.js'
import { DateRangeResponse } from '../../../../../utils/dates/date-ranges/date-range.response.js'

export class ViewPricingParametersOnDateResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, format: 'uuid' })
  pricingFormulaUuid: string

  @ApiProperty({ type: DateRangeResponse })
  effectivePeriod: DateRangeResponse

  @DatePeriodApiProperty()
  period: DatePeriod

  @ApiProperty({ type: MonetaryDto, nullable: true })
  baseCharge: MonetaryDto | null

  @ApiProperty({ type: Number, nullable: true })
  extraMinutesPerSeatTaken: number | null

  @ApiProperty({ type: Number, nullable: true })
  extraMinutesPerWheelchair: number | null

  constructor (pricingParameters: PricingParameters) {
    this.uuid = pricingParameters.uuid
    this.createdAt = pricingParameters.createdAt.toISOString()
    this.updatedAt = pricingParameters.updatedAt.toISOString()
    this.pricingFormulaUuid = pricingParameters.pricingFormulaUuid
    this.effectivePeriod = new DateRangeResponse(pricingParameters.effectivePeriod)
    this.period = pricingParameters.effectivePeriod.getDatePeriod()
    this.baseCharge = pricingParameters.baseCharge
      ? MonetaryDto.from(pricingParameters.baseCharge)
      : null
    this.extraMinutesPerSeatTaken = pricingParameters.extraMinutesPerSeatTaken
    this.extraMinutesPerWheelchair = pricingParameters.extraMinutesPerWheelchair
  }
}
