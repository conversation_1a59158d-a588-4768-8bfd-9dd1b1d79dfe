import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { ViewPricingParametersOnDateController } from './view-pricing-parameters-on-date.controller.js'
import { ViewPricingParametersOnDateUseCase } from './view-pricing-parameters-on-date.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PricingParameters])
  ],
  controllers: [ViewPricingParametersOnDateController],
  providers: [ViewPricingParametersOnDateUseCase]
})
export class ViewPricingParametersOnDateModule {}
