import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { ViewPricingParametersOnDateResponse } from './view-pricing-parameters-on-date.response.js'
import { PricingParametersNotFoundError } from './errors/pricing-parameters-not-found.error.js'

@Injectable()
export class ViewPricingParametersOnDateUseCase {
  constructor (
    @InjectRepository(PricingParameters)
    private readonly pricingParametersRepository: Repository<PricingParameters>
  ) {}

  async execute (
    pricingFormulaUuid: string,
    date: string
  ): Promise<ViewPricingParametersOnDateResponse> {
    const pricingParameters = await this.pricingParametersRepository
      .createQueryBuilder('pricing_parameters')
      .where('pricing_parameters.pricing_formula_uuid = :pricingFormulaUuid', { pricingFormulaUuid })
      .andWhere('pricing_parameters.deleted_at IS NULL')
      .andWhere(':date >= lower(effective_period) AND :date <= inc_upper(effective_period)', { date: date })
      .getOne()

    if (!pricingParameters) {
      throw new PricingParametersNotFoundError(pricingFormulaUuid)
    }

    return new ViewPricingParametersOnDateResponse(pricingParameters)
  }
}
