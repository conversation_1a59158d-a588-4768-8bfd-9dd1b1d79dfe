import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { Repository, SelectQueryBuilder } from 'typeorm'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { ViewPricingParametersOnDateUseCase } from '../view-pricing-parameters-on-date.use-case.js'
import { PricingParameters } from '../../../pricing-parameters.entity.js'
import { PricingParametersNotFoundError } from '../errors/pricing-parameters-not-found.error.js'

describe('ViewCurrentPricingParametersUseCase unit tests', () => {
  before(() => TestBench.setupUnitTest())

  it('throws an error when no pricing parameters are found for the pricing formula', async () => {
    const pricingParametersRepository = createStubInstance(Repository<PricingParameters>)

    const queryBuilder = createStubInstance(SelectQueryBuilder<PricingParameters>)

    queryBuilder.leftJoinAndSelect.returns(queryBuilder)
    queryBuilder.where.returns(queryBuilder)
    queryBuilder.andWhere.returns(queryBuilder)
    queryBuilder.getOne.resolves(null)

    pricingParametersRepository.createQueryBuilder.returns(queryBuilder)

    const useCase = new ViewPricingParametersOnDateUseCase(
      pricingParametersRepository
    )

    const pricingFormulaUuid = randomUUID()

    await expect(async () => await useCase.execute(pricingFormulaUuid, '2023-10-01'))
      .rejects.toThrow(new PricingParametersNotFoundError(pricingFormulaUuid))
  })
})
