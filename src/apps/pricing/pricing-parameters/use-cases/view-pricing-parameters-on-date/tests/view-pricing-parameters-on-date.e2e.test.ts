import { after, before, describe, it } from 'node:test'
import { stringify } from 'qs'
import { expect } from 'expect'
import request from 'supertest'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { PricingParameters } from '../../../pricing-parameters.entity.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { PricingFormula } from '../../../../pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../pricing-formula/pricing-formula.entity.builder.js'
import { PricingFormulaType } from '../../../../pricing-formula/pricing-formula-types.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { Euros } from '../../../../../../utils/monetary/euros.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PastInfinityDate } from '../../../../../../utils/dates/wise-date/past-infinity-date.js'
import { DateRangeResponse } from '../../../../../../utils/dates/date-ranges/date-range.response.js'

describe('get current pricing parameters e2e tests', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('returns the current pricing parameters when found for the pricing formula', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    const today = WiseDate.today()
    const currentEffectivePeriod = new DateRange(
      today.subtract(10, 'days'),
      today.add(10, 'days')
    )

    const pastEffectivePeriod = new DateRange(
      new PastInfinityDate(),
      today.subtract(11, 'days')
    )

    const futureEffectivePeriod = new DateRange(
      today.add(11, 'days'),
      new FutureInfinityDate()
    )

    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(currentEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15.75)
      .withExtraMinutesPerWheelchair(30.15)
      .build()

    const pastPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(pastEffectivePeriod)
      .withBaseCharge(new Euros(20))
      .withExtraMinutesPerSeatTaken(25)
      .withExtraMinutesPerWheelchair(35)
      .build()
    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod)
      .withBaseCharge(new Euros(30))
      .withExtraMinutesPerSeatTaken(35)
      .withExtraMinutesPerWheelchair(45)
      .build()
    await testSetup.dataSource.manager.insert(PricingParameters,
      [currentPricingParameters, pastPricingParameters, futurePricingParameters]
    )

    const query = {
      filter: {
        date: today.toString()
      }
    }

    const response = await request(testSetup.httpServer)
      .get(`/pricing-formulas/${pricingFormula.uuid}/pricing-parameters`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
    expect(response.body).toEqual(expect.objectContaining({
      uuid: currentPricingParameters.uuid,
      pricingFormulaUuid: pricingFormula.uuid,
      effectivePeriod: new DateRangeResponse(currentEffectivePeriod),
      baseCharge: expect.objectContaining({
        amount: 100000,
        currency: 'EUR',
        precision: 4
      }),
      extraMinutesPerSeatTaken: 15.75,
      extraMinutesPerWheelchair: 30.15
    }))
  })
})
