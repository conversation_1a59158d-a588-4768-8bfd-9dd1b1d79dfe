import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DateRange } from '../../../../../utils/dates/date-ranges/date-range.js'
import { AdjacentTo } from '../../../../../utils/typeorm/operators/adjacent-to.js'

@Injectable()
export class DeletePricingParametersRepository {
  constructor (
    @InjectRepository(PricingParameters)
    private readonly pricingParametersRepository: Repository<PricingParameters>
  ) {}

  async getPricingParameters (withUuid: string): Promise<PricingParameters | null> {
    return await this.pricingParametersRepository.findOneBy({ uuid: withUuid })
  }

  async getLeftAdjacentPricingParameters (to: PricingParameters): Promise<PricingParameters[]> {
    return await this.pricingParametersRepository.createQueryBuilder('pricing_parameters')
      .where('pricing_parameters.pricing_formula_uuid = :pricingFormulaUuid', { pricingFormulaUuid: to.pricingFormulaUuid })
      .and
  }

  async updatePricingParametersEffectivePeriod (
    uuid: string,
    effectivePeriod: DateRange
  ): Promise<void> {
    await this.pricingParametersRepository.update(
      { uuid },
      { effectivePeriod }
    )
  }
}
