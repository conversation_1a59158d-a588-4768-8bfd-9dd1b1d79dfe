import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'
import { DateRangeResponse } from '../../../../../utils/dates/date-ranges/date-range.response.js'
import { PricingParameters } from '../../pricing-parameters.entity.js'

@OneOfMeta(DomainEventLog, DomainEventType.PRICING_PARAMETERS_DELETED)
export class PricingParametersDeletedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly uuid: string

  @ApiProperty({ type: DateRangeResponse })
  readonly period: DateRangeResponse

  constructor (deletedPricingParameters: PricingParameters) {
    this.uuid = deletedPricingParameters.uuid
    this.period = new DateRangeResponse(deletedPricingParameters.effectivePeriod)
  }
}

@RegisterDomainEvent(DomainEventType.PRICING_PARAMETERS_DELETED, 1)
export class PricingParametersDeletedEvent
  extends DomainEvent<PricingParametersDeletedEventContent> {
  constructor (deletedPricingParameters: PricingParameters) {
    super({ content: new PricingParametersDeletedEventContent(deletedPricingParameters) })
  }
}
