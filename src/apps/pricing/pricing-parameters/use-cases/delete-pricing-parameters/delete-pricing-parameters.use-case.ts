import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DatePeriod } from '../../../../../utils/dates/date-period.enum.js'
import { WiseDate } from '../../../../../utils/dates/wise-date/wise-date.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { PricingParametersNotInFutureError } from '../../errors/pricing-parameters-not-in-future.error.js'
import { PricingParametersNotFoundError } from '../../errors/pricing-parameters-not-found.error.js'
import { NoLeftAdjacentPricingParametersFoundError } from '../../errors/pricing-parameters-no-left-adjacent-found.error.js'
import { DateRange } from '../../../../../utils/dates/date-ranges/date-range.js'
import { DeletePricingParametersRepository } from './delete-pricing-parameters.repository.js'
import { PricingParametersDeletedEvent } from './pricing-parameters-deleted.event.js'

@Injectable()
export class DeletePricingParametersUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly repository: DeletePricingParametersRepository
  ) {}

  async execute (withUuid: string): Promise<void> {
    const pricingParameters = await this.repository.getPricingParameters(withUuid)

    if (pricingParameters === null) {
      throw new PricingParametersNotFoundError(withUuid)
    }

    const datePeriod = pricingParameters.effectivePeriod.getDatePeriod(WiseDate.today())
    if (datePeriod !== DatePeriod.FUTURE) {
      throw new PricingParametersNotInFutureError(withUuid)
    }

    const adjacentParameters = await this.repository.getLeftAdjacentPricingParameters(
      pricingParameters
    )

    if (adjacentParameters.length === 0) {
      throw new NoLeftAdjacentPricingParametersFoundError(withUuid)
    }

    const event = new PricingParametersDeletedEvent(pricingParameters)

    await transaction(this.dataSource, async () => {
      await this.repository.updatePricingParametersEffectivePeriod(
        adjacentParameters[0].uuid,
        new DateRange(
          adjacentParameters[0].effectivePeriod.startDate,
          pricingParameters.effectivePeriod.endDate
        )
      )

      await this.repository.deletePricingParameters(withUuid)
      await this.eventEmitter.emitOne(event)
    })
  }
}
