import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DatePeriod } from '../../../../../utils/dates/date-period.enum.js'
import { WiseDate } from '../../../../../utils/dates/wise-date/wise-date.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { PricingParametersNotInFutureError } from '../update-pricing-parameters/errors/pricing-parameters-not-in-future.error.js'
import { PricingParametersNotFoundError } from '../view-pricing-parameters-on-date/errors/pricing-parameters-not-found.error.js'
import { DeletePricingParametersRepository } from './delete-pricing-parameters.repository.js'

@Injectable()
export class DeletePricingParametersUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly repository: DeletePricingParametersRepository
  ) {}

  async deletePricingParameters (withUuid: string): Promise<void> {
    const pricingParameters = await this.repository.getPricingParameters(withUuid)

    if (pricingParameters === null) {
      throw new PricingParametersNotFoundError(withUuid)
    }

    const datePeriod = pricingParameters.effectivePeriod.getDatePeriod(WiseDate.today())
    if (datePeriod !== DatePeriod.FUTURE) {
      throw new PricingParametersNotInFutureError(withUuid)
    }
    await transaction(this.dataSource, async () => {

    })
  }
}
