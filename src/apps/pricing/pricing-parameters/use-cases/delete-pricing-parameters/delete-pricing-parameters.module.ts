import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DeletePricingParametersController } from './delete-pricing-parameters.controller.js'
import { DeletePricingParametersUseCase } from './delete-pricing-parameters.use-case.js'
import { DeletePricingParametersRepository } from './delete-pricing-parameters.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PricingParameters])
  ],
  controllers: [DeletePricingParametersController],
  providers: [
    DeletePricingParametersUseCase,
    DeletePricingParametersRepository
  ]
})
export class DeletePricingParametersModule {}
