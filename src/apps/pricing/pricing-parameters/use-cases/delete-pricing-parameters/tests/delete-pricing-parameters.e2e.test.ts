import { afterEach, beforeEach, describe, it } from 'node:test'
import { HttpStatus } from '@nestjs/common'
import request from 'supertest'
import { expect } from 'expect'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { PricingFormula } from '../../../../pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaEntityBuilder } from '../../../../pricing-formula/pricing-formula.entity.builder.js'
import { PricingFormulaType } from '../../../../pricing-formula/pricing-formula-types.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { Euros } from '../../../../../../utils/monetary/euros.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PastInfinityDate } from '../../../../../../utils/dates/wise-date/past-infinity-date.js'
import { PricingParameters } from '../../../pricing-parameters.entity.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'

describe('DeletePricingParametersUseCase E2E', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser

  beforeEach(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
  })

  afterEach(async () => {
    await testSetup.teardown()
  })

  it('successfully deletes future pricing parameters and extends previous period', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    // Create previous pricing parameters
    const previousEffectivePeriod = new DateRange(
      new PastInfinityDate(),
      WiseDate.today()
    )

    const previousPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(previousEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    // Create future pricing parameters to delete
    const futureEffectivePeriod = new DateRange(
      WiseDate.tomorrow(),
      new FutureInfinityDate()
    )

    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod)
      .withBaseCharge(new Euros(2000, 2))
      .withExtraMinutesPerSeatTaken(5)
      .withExtraMinutesPerWheelchair(10)
      .build()

    await testSetup.dataSource.manager.insert(PricingParameters, [
      previousPricingParameters,
      futurePricingParameters
    ])

    const response = await request(testSetup.httpServer)
      .delete(`/pricing-parameters/${futurePricingParameters.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(HttpStatus.NO_CONTENT)

    // Verify the future pricing parameters were deleted
    const deletedPricingParameters = await testSetup.dataSource.manager.findOne(PricingParameters, {
      where: { uuid: futurePricingParameters.uuid },
      withDeleted: true
    })
    expect(deletedPricingParameters!.deletedAt).not.toBeNull()

    // Verify the previous pricing parameters were extended
    const remainingParameters = await testSetup.dataSource.manager.find(PricingParameters, {
      where: { pricingFormulaUuid: pricingFormula.uuid },
      withDeleted: false
    })

    expect(remainingParameters).toHaveLength(1)
    expect(remainingParameters[0].effectivePeriod.endDate.toString()).toBe('infinity')
  })

  it('returns 404 when pricing parameters do not exist', async () => {
    const nonExistentUuid = '00000000-0000-0000-0000-000000000000'

    const response = await request(testSetup.httpServer)
      .delete(`/pricing-parameters/${nonExistentUuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(HttpStatus.NOT_FOUND)
  })

  it('returns 400 when trying to delete current pricing parameters', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    // Create current pricing parameters
    const currentEffectivePeriod = new DateRange(
      WiseDate.today().subtract(1, 'day'),
      WiseDate.today().add(1, 'day')
    )

    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(currentEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    await testSetup.dataSource.manager.insert(PricingParameters, currentPricingParameters)

    const response = await request(testSetup.httpServer)
      .delete(`/pricing-parameters/${currentPricingParameters.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(HttpStatus.BAD_REQUEST)
    expect(response.body.code).toBe('pricing_parameters_not_in_future')
  })

  it('returns 400 when trying to delete the only pricing parameters', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    // Create only future pricing parameters (no adjacent ones)
    const futureEffectivePeriod = new DateRange(
      WiseDate.tomorrow(),
      new FutureInfinityDate()
    )

    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    await testSetup.dataSource.manager.insert(PricingParameters, futurePricingParameters)

    const response = await request(testSetup.httpServer)
      .delete(`/pricing-parameters/${futurePricingParameters.uuid}`)
      .set('Authorization', `Bearer ${adminUser.token}`)

    expect(response).toHaveStatus(HttpStatus.BAD_REQUEST)
    expect(response.body.code).toBe('only_pricing_parameters_not_deletable')
  })
})
