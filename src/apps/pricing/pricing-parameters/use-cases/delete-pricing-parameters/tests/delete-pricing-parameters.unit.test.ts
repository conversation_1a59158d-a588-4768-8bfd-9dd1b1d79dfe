import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { DeletePricingParametersRepository } from '../delete-pricing-parameters.repository.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { DeletePricingParametersUseCase } from '../delete-pricing-parameters.use-case.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PastInfinityDate } from '../../../../../../utils/dates/wise-date/past-infinity-date.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { HistoricValueRevision } from '../../../../../../utils/dates/historic-value-reviser/historic-value-revision.js'
import { PricingParametersDeletedEvent } from '../pricing-parameters-deleted.event.js'

describe('DeletePricingParametersUseCase', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('should throw an error when the pricing parameters do not exist', async () => {
    const repository = createStubInstance(DeletePricingParametersRepository)
    repository.getPricingParameters.resolves(null)

    const useCase = new DeletePricingParametersUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      repository,
    )

    const pricingParametersUuid = randomUUID()

    await expect(useCase.execute(pricingParametersUuid))
      .rejects.toThrow(new PricingParametersNotFoundError(pricingParametersUuid))
  })

  it('should throw an error when the pricing parameters are not in the future', async () => {
    const repository = createStubInstance(DeletePricingParametersRepository)

    // Create pricing parameters that are active (current)
    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(
        WiseDate.today().subtract(1, 'day'),
        WiseDate.today().add(1, 'day')
      ))
      .build()

    repository.getPricingParameters.resolves(currentPricingParameters)

    const useCase = new DeletePricingParametersUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      repository,
    )

    const pricingParametersUuid = randomUUID()

    await expect(useCase.execute(pricingParametersUuid))
      .rejects.toThrow(new PricingParametersNotInFutureError(pricingParametersUuid))
  })

  it('should throw an error when there are no adjacent pricing parameters', async () => {
    const repository = createStubInstance(DeletePricingParametersRepository)

    // Create pricing parameters that are in the future
    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(
        WiseDate.today().add(1, 'day'),
        new FutureInfinityDate()
      ))
      .build()

    repository.getPricingParameters.resolves(futurePricingParameters)

    const useCase = new DeletePricingParametersUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      repository,
      createStubInstance(HistoricPricingParametersReviser)
    )

    const pricingParametersUuid = randomUUID()

    await expect(useCase.execute(pricingParametersUuid))
      .rejects.toThrow(new OnlyPricingParametersNotDeletableError())
  })

  it('should successfully delete future pricing parameters and extend previous period', async () => {
    const repository = createStubInstance(DeletePricingParametersRepository)
    const reviser = createStubInstance(HistoricPricingParametersReviser)
    const eventEmitter = createStubInstance(DomainEventEmitter)

    // Create pricing parameters that are in the future
    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(
        WiseDate.today().add(1, 'day'),
        new FutureInfinityDate()
      ))
      .build()

    // Create previous pricing parameters
    const previousPricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(
        new PastInfinityDate(),
        WiseDate.today()
      ))
      .build()

    // Expected extended pricing parameters
    const extendedPricingParameters = new PricingParametersEntityBuilder()
      .copy(previousPricingParameters)
      .withEffectivePeriod(new DateRange(
        new PastInfinityDate(),
        new FutureInfinityDate()
      ))
      .build()

    repository.getPricingParameters.resolves(futurePricingParameters)
    repository.getAdjacentPricingParameters.resolves([previousPricingParameters])

    reviser.reviseRemoval.returns(new HistoricValueRevision(
      [previousPricingParameters],
      [extendedPricingParameters]
    ))

    const useCase = new DeletePricingParametersUseCase(
      stubDataSource(),
      eventEmitter,
      repository,
      reviser
    )

    const pricingParametersUuid = randomUUID()

    await expect(useCase.execute(pricingParametersUuid))
      .resolves.not.toThrow()

    expect(eventEmitter.emitOne.calledOnce).toBe(true)
    const emittedEvent = eventEmitter.emitOne.getCall(0).args[0] as PricingParametersDeletedEvent
    expect(emittedEvent).toBeInstanceOf(PricingParametersDeletedEvent)
    expect(emittedEvent.content.uuid).toBe(futurePricingParameters.uuid)
  })
})
