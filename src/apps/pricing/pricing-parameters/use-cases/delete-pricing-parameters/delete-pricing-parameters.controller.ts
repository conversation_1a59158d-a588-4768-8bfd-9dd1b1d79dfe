import { Controller, Delete, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { PricingParametersNotInFutureError } from '../update-pricing-parameters/errors/pricing-parameters-not-in-future.error.js'
import { PricingParametersNotFoundError } from '../view-pricing-parameters-on-date/errors/pricing-parameters-not-found.error.js'
import { DeletePricingParametersUseCase } from './delete-pricing-parameters.use-case.js'

@ApiTags('PricingParameters')
@ApiOAuth2([])
@Controller('pricing-parameters/:pricingParametersUuid')
export class DeletePricingParametersController {
  constructor (
    private readonly deletePricingParametersUseCase: DeletePricingParametersUseCase
  ) {}

  @Delete()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNotFoundErrorResponse(PricingParametersNotFoundError)
  @ApiBadRequestErrorResponse(PricingParametersNotInFutureError)
  async deletePricingParameters (
    @UuidParam('pricingParametersUuid') pricingParametersUuid: string
  ): Promise<void> {
    await this.deletePricingParametersUseCase.deletePricingParameters(pricingParametersUuid)
  }
}
