import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DateRange } from '../../../../../utils/dates/date-ranges/date-range.js'

export class CreatePricingParametersOnDateRepository {
  constructor (
    @InjectRepository(PricingParameters)
    private readonly pricingParametersRepo: Repository<PricingParameters>
  ) {}

  async getParametersOnDate (
    pricingFormulaUuid: string,
    effectiveDate: string
  ): Promise<PricingParameters | null> {
    return this.pricingParametersRepo
      .createQueryBuilder('pricing_parameters')
      .where('pricing_parameters.pricing_formula_uuid = :pricingFormulaUuid', { pricingFormulaUuid })
      .andWhere('pricing_parameters.deleted_at IS NULL')
      .andWhere(':date >= lower(effective_period) AND :date <= inc_upper(effective_period)', { date: effectiveDate })
      .getOne()
  }

  async deleteFutureParameters (
    pricingFormulaUuid: string,
    effectiveDate: string
  ): Promise<void> {
    await this.pricingParametersRepo
      .createQueryBuilder('pricing_parameters')
      .softDelete()
      .where('pricing_parameters.pricing_formula_uuid = :pricingFormulaUuid', { pricingFormulaUuid })
      .andWhere('pricing_parameters.deleted_at IS NULL')
      .andWhere('lower(effective_period) > :effectiveDate', { effectiveDate })
      .execute()
  }

  async deleteByUuid (
    pricingParameterUuid: string
  ): Promise<void> {
    await this.pricingParametersRepo.softDelete ({ uuid: pricingParameterUuid })
  }

  async updateEffectivePeriod (
    pricingParameterUuid: string,
    effectivePeriod: DateRange
  ): Promise<void> {
    await this.pricingParametersRepo.update(
      { uuid: pricingParameterUuid },
      { effectivePeriod }
    )
  }

  async insert (pricingParameters: PricingParameters): Promise<void> {
    await this.pricingParametersRepo.insert(pricingParameters)
  }
}
