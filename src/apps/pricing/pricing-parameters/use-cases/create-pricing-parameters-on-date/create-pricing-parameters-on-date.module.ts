import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DomainEventEmitterModule } from '../../../../../modules/domain-events/domain-event-emitter.module.js'
import { CreatePricingParametersOnDateController } from './create-pricing-parameters-on-date.controller.js'
import { CreatePricingParametersOnDateUseCase } from './create-pricing-parameters-on-date.use-case.js'
import { CreatePricingParametersOnDateRepository } from './create-pricing-parameters-on-date.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PricingParameters]),
    DomainEventEmitterModule
  ],
  controllers: [CreatePricingParametersOnDateController],
  providers: [
    CreatePricingParametersOnDateUseCase,
    CreatePricingParametersOnDateRepository
  ]
})
export class CreatePricingParametersOnDateModule {}
