import { Body, Controller, Post } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiNotFoundErrorResponse } from '../../../../../modules/exceptions/api-errors/api-error-response.decorator.js'
import { PricingParametersNotFoundError } from '../../errors/pricing-parameters-not-found.error.js'
import { CreatePricingParametersOnDateUseCase } from './create-pricing-parameters-on-date.use-case.js'
import { CreatePricingParametersOnDateCommand } from './create-pricing-parameters-on-date.command.js'

@ApiTags('PricingParameters')
@ApiOAuth2([])
@Controller('pricing-formulas/:pricingFormulaUuid/pricing-parameters')
export class CreatePricingParametersOnDateController {
  constructor (
    private readonly createPricingParametersOnDateUseCase: CreatePricingParametersOnDateUseCase
  ) {}

  @Post()
  @ApiNotFoundErrorResponse(PricingParametersNotFoundError)
  async createPricingParametersOnDate (
    @UuidParam('pricingFormulaUuid') pricingFormulaUuid: string,
    @Body() command: CreatePricingParametersOnDateCommand
  ): Promise<void> {
    await this.createPricingParametersOnDateUseCase.execute(
      pricingFormulaUuid,
      command
    )
  }
}
