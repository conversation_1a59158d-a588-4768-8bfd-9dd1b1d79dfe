import { Injectable } from '@nestjs/common'
import { DataSource } from 'typeorm'
import { transaction } from '@wisemen/nestjs-typeorm'
import { DateRange } from '../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../utils/dates/wise-date/wise-date.js'
import { FutureInfinityDate } from '../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PricingParametersNotFoundError } from '../view-pricing-parameters-on-date/errors/pricing-parameters-not-found.error.js'
import { PricingParametersEntityBuilder } from '../../pricing-parameters.entity.builder.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { Euros } from '../../../../../utils/monetary/euros.js'
import { CreatePricingParametersOnDateCommand } from './create-pricing-parameters-on-date.command.js'
import { CreatePricingParametersOnDateRepository } from './create-pricing-parameters-on-date.repository.js'
import { PricingParametersCreatedEvent } from './pricing-parameters-created.event.js'

@Injectable()
export class CreatePricingParametersOnDateUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter,
    private readonly pricingParametersRepository: CreatePricingParametersOnDateRepository
  ) {}

  async execute (
    pricingFormulaUuid: string,
    command: CreatePricingParametersOnDateCommand
  ): Promise<void> {
    const pricingParametersOnEffectiveDate
     = await this.pricingParametersRepository.getParametersOnDate(
       pricingFormulaUuid,
       command.effectiveDate
     )

    if (!pricingParametersOnEffectiveDate) {
      throw new PricingParametersNotFoundError(pricingFormulaUuid)
    }

    const endDateNewPricingParameters = command.overwriteFutureParameters
      ? new FutureInfinityDate()
      : pricingParametersOnEffectiveDate.effectivePeriod.endDate

    const newPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormulaUuid)
      .withEffectivePeriod(new DateRange(
        new WiseDate(command.effectiveDate),
        endDateNewPricingParameters
      ))
      .withBaseCharge(command.baseCharge ? new Euros(command.baseCharge) : null)
      .withExtraMinutesPerSeatTaken(command.extraMinutesPerSeatTaken)
      .withExtraMinutesPerWheelchair(command.extraMinutesPerWheelchair)
      .build()

    const event = new PricingParametersCreatedEvent(
      pricingFormulaUuid,
      command.effectiveDate
    )

    await transaction(this.dataSource, async () => {
      if (command.overwriteFutureParameters) {
        await this.pricingParametersRepository.deleteFutureParameters(
          pricingFormulaUuid,
          command.effectiveDate
        )
      }

      if (pricingParametersOnEffectiveDate.effectivePeriod.startDate
        .isSame(new WiseDate(command.effectiveDate))) {
        await this.pricingParametersRepository.deleteByUuid(
          pricingParametersOnEffectiveDate.uuid
        )
      } else {
        const newEffectivePeriod = new DateRange(
          pricingParametersOnEffectiveDate.effectivePeriod.startDate,
          new WiseDate(command.effectiveDate).subtract(1, 'day')
        )
        await this.pricingParametersRepository.updateEffectivePeriod(
          pricingParametersOnEffectiveDate.uuid,
          newEffectivePeriod
        )
      }
      await this.pricingParametersRepository.insert(newPricingParameters)
      await this.eventEmitter.emitOne(event)
    })
  }
}
