import { Currency, MonetaryDtoBuilder } from '@wisemen/monetary'
import { WiseDate } from '../../../../../utils/dates/wise-date/wise-date.js'
import { CreatePricingParametersOnDateCommand } from './create-pricing-parameters-on-date.command.js'

export class CreatePricingParametersOnDateCommandBuilder {
  private readonly command: CreatePricingParametersOnDateCommand

  constructor () {
    this.command = new CreatePricingParametersOnDateCommand()
    this.command.effectiveDate = WiseDate.tomorrow().toString()
    this.command.baseCharge = null
    this.command.extraMinutesPerSeatTaken = null
    this.command.extraMinutesPerWheelchair = null
    this.command.overwriteFutureParameters = false
  }

  withEffectiveDate (date: string): this {
    this.command.effectiveDate = date
    return this
  }

  withBaseCharge (baseCharge: number): this {
    this.command.baseCharge = new MonetaryDtoBuilder()
      .withAmount(baseCharge)
      .withCurrency(Currency.EUR)
      .withPrecision(2)
      .build()
    return this
  }

  withExtraMinutesPerSeatTaken (extraMinutesPerSeatTaken: number): this {
    this.command.extraMinutesPerSeatTaken = extraMinutesPerSeatTaken
    return this
  }

  withExtraMinutesPerWheelchair (extraMinutesPerWheelchair: number): this {
    this.command.extraMinutesPerWheelchair = extraMinutesPerWheelchair
    return this
  }

  withOverwriteFutureParameters (deleteFutureParameters: boolean): this {
    this.command.overwriteFutureParameters = deleteFutureParameters
    return this
  }

  build (): CreatePricingParametersOnDateCommand {
    return this.command
  }
}
