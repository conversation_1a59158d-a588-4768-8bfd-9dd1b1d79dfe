import { ApiProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsNumber, IsPositive } from 'class-validator'
import { MonetaryDto } from '@wisemen/monetary'
import { IsNullable } from '@wisemen/validators'
import { IsAfterTodayString } from '../../../../../utils/validators/is-after-today.js'
import { EurosApiProperty, IsEuros } from '../../../../../utils/monetary/euros.js'

export class CreatePricingParametersOnDateCommand {
  @ApiProperty({
    type: String,
    format: 'date',
    description: 'The date from which the pricing parameters should be effective. Must be in the future.'
  })
  @IsNotEmpty()
  @IsAfterTodayString()
  effectiveDate: string

  @EurosApiProperty({ nullable: true })
  @IsNullable()
  @IsEuros()
  baseCharge: MonetaryDto | null

  @ApiProperty({
    type: Number,
    format: 'float',
    nullable: true,
    description: 'The number of extra minutes per seat taken.'
  })
  @IsNullable()
  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  extraMinutesPerSeatTaken: number | null

  @ApiProperty({
    type: Number,
    format: 'float',
    nullable: true,
    description: 'The number of extra minutes per wheelchair.'
  })
  @IsNullable()
  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  extraMinutesPerWheelchair: number | null

  @ApiProperty({
    type: Boolean,
    description: 'Whether all future pricing parameters should be deleted. If true, the effective period will extend to infinity.'
  })
  @IsBoolean()
  overwriteFutureParameters: boolean
}
