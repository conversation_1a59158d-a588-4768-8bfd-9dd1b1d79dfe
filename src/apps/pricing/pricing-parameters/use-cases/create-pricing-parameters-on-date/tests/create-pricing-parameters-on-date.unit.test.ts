import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { CreatePricingParametersOnDateRepository } from '../create-pricing-parameters-on-date.repository.js'
import { stubDataSource } from '../../../../../../../test/utils/stub-datasource.js'
import { CreatePricingParametersOnDateCommandBuilder } from '../create-pricing-parameters-on-date.command.builder.js'
import { CreatePricingParametersOnDateUseCase } from '../create-pricing-parameters-on-date.use-case.js'
import { DomainEventEmitter } from '../../../../../../modules/domain-events/domain-event-emitter.js'
import { PricingParametersCreatedEvent } from '../pricing-parameters-created.event.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { PricingParametersNotFoundError } from '../../../errors/pricing-parameters-not-found.error.js'

describe('CreatePricingParametersOnDateUseCase', () => {
  before(() => {
    TestBench.setupUnitTest()
  })

  it('should trow an error when the pricing parameters do not exist', async () => {
    const pricingParametersRepository = createStubInstance(CreatePricingParametersOnDateRepository)
    pricingParametersRepository.getParametersOnDate.resolves(null)

    const useCase = new CreatePricingParametersOnDateUseCase(
      stubDataSource(),
      createStubInstance(DomainEventEmitter),
      pricingParametersRepository
    )

    const pricingFormulaUuid = randomUUID()
    const command = new CreatePricingParametersOnDateCommandBuilder().build()

    await expect(useCase.execute(pricingFormulaUuid, command))
      .rejects.toThrow(new PricingParametersNotFoundError(pricingFormulaUuid))
  })

  it('should delete the pricing parameters of the range when the effective date is equal to the start date of the effective period', async () => {
    const pricingParametersRepository = createStubInstance(CreatePricingParametersOnDateRepository)
    const pricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(new WiseDate(), new FutureInfinityDate()))
      .build()
    pricingParametersRepository.getParametersOnDate.resolves(pricingParameters)
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const useCase = new CreatePricingParametersOnDateUseCase(
      stubDataSource(),
      eventEmitter,
      pricingParametersRepository
    )
    const pricingFormulaUuid = randomUUID()
    const command = new CreatePricingParametersOnDateCommandBuilder()
      .withEffectiveDate(pricingParameters.effectivePeriod.startDate.toString())
      .withOverwriteFutureParameters(true)
      .build()
    await useCase.execute(pricingFormulaUuid, command)
    expect(pricingParametersRepository.deleteByUuid.firstCall.firstArg).toBe(pricingParameters.uuid)
  })

  it('should emit pricing parameters created event', async () => {
    const pricingParametersRepository = createStubInstance(CreatePricingParametersOnDateRepository)
    const pricingParameters = new PricingParametersEntityBuilder()
      .withEffectivePeriod(new DateRange(new WiseDate(), new FutureInfinityDate()))
      .build()
    pricingParametersRepository.getParametersOnDate.resolves(pricingParameters)
    const eventEmitter = createStubInstance(DomainEventEmitter)
    const useCase = new CreatePricingParametersOnDateUseCase(
      stubDataSource(),
      eventEmitter,
      pricingParametersRepository
    )

    const pricingFormulaUuid = randomUUID()
    const command = new CreatePricingParametersOnDateCommandBuilder().build()

    await useCase.execute(pricingFormulaUuid, command)

    const expectedEvent = new PricingParametersCreatedEvent(
      pricingFormulaUuid,
      command.effectiveDate
    )
    expect(eventEmitter).toHaveEmitted(expectedEvent)
  })
})
