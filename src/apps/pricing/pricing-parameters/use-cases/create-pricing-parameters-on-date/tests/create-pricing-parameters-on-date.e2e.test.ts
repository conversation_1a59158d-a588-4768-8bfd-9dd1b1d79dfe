import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { SortDirection } from '@wisemen/pagination'
import { Currency, Monetary } from '@wisemen/monetary'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { Euros } from '../../../../../../utils/monetary/euros.js'
import { PricingFormula } from '../../../../pricing-formula/entities/pricing-formula.entity.js'
import { PricingFormulaType } from '../../../../pricing-formula/pricing-formula-types.js'
import { PricingFormulaEntityBuilder } from '../../../../pricing-formula/pricing-formula.entity.builder.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { PricingParameters } from '../../../pricing-parameters.entity.js'
import { PastInfinityDate } from '../../../../../../utils/dates/wise-date/past-infinity-date.js'
import { CreatePricingParametersOnDateCommandBuilder } from '../create-pricing-parameters-on-date.command.builder.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'

describe('Create pricing parameters on date e2e', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser

  before(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
  })

  after(async () => {
    await testSetup.teardown()
  })

  it('Should split the effective period, create a new pricing parameters and delete future pricing parameters', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    const currentEffectivePeriod = new DateRange(
      new PastInfinityDate(),
      WiseDate.today().add(10, 'days')
    )

    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(currentEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    const futureEffectivePeriod = new DateRange(
      WiseDate.today().add(11, 'days'),
      new FutureInfinityDate()
    )

    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    await testSetup.dataSource.manager.insert(
      PricingParameters,
      [currentPricingParameters, futurePricingParameters]
    )

    const command = new CreatePricingParametersOnDateCommandBuilder()
      .withEffectiveDate(WiseDate.tomorrow().toString())
      .withBaseCharge(1500)
      .withExtraMinutesPerSeatTaken(2)
      .withExtraMinutesPerWheelchair(4)
      .withOverwriteFutureParameters(true)
      .build()

    const response = await request(testSetup.httpServer)
      .post(`/pricing-formulas/${pricingFormula.uuid}/pricing-parameters`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)

    const allPricingParameters = await testSetup.dataSource.manager.find(PricingParameters, {
      where: {
        pricingFormulaUuid: pricingFormula.uuid
      },
      order: {
        effectivePeriod: SortDirection.ASC
      },
      withDeleted: false
    })
    expect(allPricingParameters).toHaveLength(2)
    expect(allPricingParameters[0].effectivePeriod).toEqual(
      new DateRange(
        new PastInfinityDate(),
        WiseDate.tomorrow().subtract(1, 'day')
      )
    )
    expect(allPricingParameters[1].effectivePeriod).toEqual(
      new DateRange(
        WiseDate.tomorrow(),
        new FutureInfinityDate()
      )
    )

    expect(allPricingParameters[1].baseCharge).toEqual(new Monetary(150000, Currency.EUR, 4))
    expect(allPricingParameters[1].extraMinutesPerSeatTaken).toEqual(2)
    expect(allPricingParameters[1].extraMinutesPerWheelchair).toEqual(4)
  })

  it('Should split the effective period, create a new pricing parameters and keep future pricing parameters', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    const currentEffectivePeriod = new DateRange(
      new PastInfinityDate(),
      WiseDate.today().add(10, 'days')
    )

    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(currentEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()
    const futureEffectivePeriod = new DateRange(
      WiseDate.today().add(11, 'days'),
      new FutureInfinityDate()
    )
    const futurePricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod)
      .withBaseCharge(new Euros(1000, 2))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()
    await testSetup.dataSource.manager.insert(
      PricingParameters,
      [currentPricingParameters, futurePricingParameters]
    )
    const command = new CreatePricingParametersOnDateCommandBuilder()
      .withEffectiveDate(WiseDate.tomorrow().toString())
      .withBaseCharge(1500)
      .withExtraMinutesPerSeatTaken(2)
      .withExtraMinutesPerWheelchair(4)
      .withOverwriteFutureParameters(false)
      .build()
    const response = await request(testSetup.httpServer)
      .post(`/pricing-formulas/${pricingFormula.uuid}/pricing-parameters`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .send(command)
    expect(response).toHaveStatus(201)
    const allPricingParameters = await testSetup.dataSource.manager.find(PricingParameters, {
      where: {
        pricingFormulaUuid: pricingFormula.uuid
      },
      order: {
        effectivePeriod: SortDirection.ASC
      },
      withDeleted: false
    })
    expect(allPricingParameters).toHaveLength(3)
    expect(allPricingParameters[0].effectivePeriod).toEqual(
      new DateRange(
        new PastInfinityDate(),
        WiseDate.tomorrow().subtract(1, 'day')
      )
    )
    expect(allPricingParameters[1].effectivePeriod).toEqual(
      new DateRange(
        WiseDate.tomorrow(),
        WiseDate.today().add(10, 'days')
      )
    )

    expect(allPricingParameters[1].baseCharge).toEqual(new Monetary(150000, Currency.EUR, 4))
    expect(allPricingParameters[1].extraMinutesPerSeatTaken).toEqual(2)
    expect(allPricingParameters[1].extraMinutesPerWheelchair).toEqual(4)
  })
})
