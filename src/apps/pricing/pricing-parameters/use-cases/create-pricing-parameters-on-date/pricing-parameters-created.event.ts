import { ApiProperty } from '@nestjs/swagger'
import { OneOfMeta } from '@wisemen/one-of'
import { DomainEvent } from '../../../../../modules/domain-events/domain-event.js'
import { DomainEventType } from '../../../../../modules/domain-events/domain-event-type.js'
import { DomainEventLog } from '../../../../../modules/domain-event-log/domain-event-log.entity.js'
import { RegisterDomainEvent } from '../../../../../modules/domain-events/register-domain-event.decorator.js'

@OneOfMeta(DomainEventLog, DomainEventType.PRICING_PARAMETERS_CREATED)
export class PricingParametersCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly pricingParametersUuid: string

  @ApiProperty({ format: 'date' })
  readonly effectiveDate: string

  constructor (pricingParametersUuid: string, effectiveDate: string) {
    this.pricingParametersUuid = pricingParametersUuid
    this.effectiveDate = effectiveDate
  }
}

@RegisterDomainEvent(DomainEventType.PRICING_PARAMETERS_CREATED, 1)
export class PricingParametersCreatedEvent
  extends DomainEvent<PricingParametersCreatedEventContent> {
  constructor (pricingParametersUuid: string, effectiveDate: string) {
    super({
      content: new PricingParametersCreatedEventContent(pricingParametersUuid, effectiveDate)
    })
  }
}
