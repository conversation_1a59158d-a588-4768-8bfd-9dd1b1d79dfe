import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { DatePeriod, DatePeriodApiProperty } from '../../../../../utils/dates/date-period.enum.js'
import { DateRangeResponse } from '../../../../../utils/dates/date-ranges/date-range.response.js'

export class ViewPricingParametersByFormulaResponseItem {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'uuid' })
  pricingFormulaUuid: string

  @ApiProperty({ type: DateRangeResponse })
  effectivePeriod: DateRangeResponse

  @DatePeriodApiProperty()
  period: DatePeriod

  constructor (pricingParameters: PricingParameters) {
    this.uuid = pricingParameters.uuid
    this.pricingFormulaUuid = pricingParameters.pricingFormulaUuid
    this.effectivePeriod = new DateRangeResponse(pricingParameters.effectivePeriod)
    this.period = pricingParameters.effectivePeriod.getDatePeriod()
  }
}

export class ViewPricingParametersByFormulaResponse
  extends PaginatedOffsetResponse<ViewPricingParametersByFormulaResponseItem> {
  @ApiProperty({ type: ViewPricingParametersByFormulaResponseItem, isArray: true })
  declare items: ViewPricingParametersByFormulaResponseItem[]

  constructor (
    pricingParameters: PricingParameters[],
    total: number,
    limit: number,
    offset: number
  ) {
    const result = pricingParameters.map(
      parameters => new ViewPricingParametersByFormulaResponseItem(parameters)
    )

    super(result, total, limit, offset)
  }
}
