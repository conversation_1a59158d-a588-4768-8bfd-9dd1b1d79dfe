import { PaginatedOffsetQuery } from '@wisemen/pagination'
import { ViewPricingParametersByFormulaQuery } from '../view-pricing-parameters-ranges.query.js'

export class ViewPricingParametersByFormulaQueryBuilder {
  private query: ViewPricingParametersByFormulaQuery

  constructor () {
    this.query = new ViewPricingParametersByFormulaQuery()
  }

  withLimit (limit: number): this {
    this.query.pagination ??= new PaginatedOffsetQuery()
    this.query.pagination.limit = limit
    return this
  }

  withOffset (offset: number): this {
    this.query.pagination ??= new PaginatedOffsetQuery()
    this.query.pagination.offset = offset
    return this
  }

  build (): ViewPricingParametersByFormulaQuery {
    return this.query
  }
}
