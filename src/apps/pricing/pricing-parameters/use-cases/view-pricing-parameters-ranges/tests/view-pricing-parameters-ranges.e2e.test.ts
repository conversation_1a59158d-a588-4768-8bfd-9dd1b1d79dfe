import { describe, it, beforeEach, afterEach } from 'node:test'
import { HttpStatus } from '@nestjs/common'
import request from 'supertest'
import { expect } from 'expect'
import { stringify } from 'qs'
import { PricingFormulaType } from '../../../../pricing-formula/pricing-formula-types.js'
import { TestUser } from '../../../../../../app/users/tests/setup-user.type.js'
import { DateRange } from '../../../../../../utils/dates/date-ranges/date-range.js'
import { WiseDate } from '../../../../../../utils/dates/wise-date/wise-date.js'
import { Euros } from '../../../../../../utils/monetary/euros.js'
import { FutureInfinityDate } from '../../../../../../utils/dates/wise-date/future-infinity-date.js'
import { PastInfinityDate } from '../../../../../../utils/dates/wise-date/past-infinity-date.js'
import { PricingFormula } from '../../../../pricing-formula/entities/pricing-formula.entity.js'
import { PricingParameters } from '../../../pricing-parameters.entity.js'
import { PricingParametersEntityBuilder } from '../../../pricing-parameters.entity.builder.js'
import { TestBench } from '../../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../../test/setup/end-to-end-test-setup.js'
import { PricingFormulaEntityBuilder } from '../../../../pricing-formula/pricing-formula.entity.builder.js'
import { DatePeriod } from '../../../../../../utils/dates/date-period.enum.js'
import { ViewPricingParametersByFormulaQueryBuilder } from './view-pricing-parameters-ranges.query.builder.js'

describe('ViewPricingParametersByFormulaUseCase', () => {
  let testSetup: EndToEndTestSetup
  let adminUser: TestUser

  beforeEach(async () => {
    testSetup = await TestBench.setupEndToEndTest()
    adminUser = await testSetup.authContext.getAdminUser()
  })

  afterEach(async () => {
    await testSetup.teardown()
  })

  it('returns all pricing parameters for a pricing formula ordered by future, current, past', async () => {
    const pricingFormula = new PricingFormulaEntityBuilder()
      .withType(PricingFormulaType.HENDRIKS_FORMULA)
      .withLastUpdatedByUserUuid(adminUser.user.uuid)
      .build()
    await testSetup.dataSource.manager.insert(PricingFormula, pricingFormula)

    const today = WiseDate.today()

    // Current pricing parameters
    const currentEffectivePeriod = new DateRange(
      today.subtract(10, 'days'),
      today.add(10, 'days')
    )

    // Past pricing parameters
    const pastEffectivePeriod1 = new DateRange(
      new PastInfinityDate(),
      today.subtract(30, 'days')
    )

    const pastEffectivePeriod2 = new DateRange(
      today.subtract(29, 'days'),
      today.subtract(11, 'days')
    )

    // Future pricing parameters
    const futureEffectivePeriod1 = new DateRange(
      today.add(11, 'days'),
      today.add(20, 'days')
    )

    const futureEffectivePeriod2 = new DateRange(
      today.add(21, 'days'),
      new FutureInfinityDate()
    )

    // Create pricing parameters
    const currentPricingParameters = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(currentEffectivePeriod)
      .withBaseCharge(new Euros(10))
      .withExtraMinutesPerSeatTaken(15)
      .withExtraMinutesPerWheelchair(30)
      .build()

    const pastPricingParameters1 = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(pastEffectivePeriod1)
      .withBaseCharge(new Euros(5))
      .withExtraMinutesPerSeatTaken(10)
      .withExtraMinutesPerWheelchair(20)
      .build()

    const pastPricingParameters2 = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(pastEffectivePeriod2)
      .withBaseCharge(new Euros(7))
      .withExtraMinutesPerSeatTaken(12)
      .withExtraMinutesPerWheelchair(25)
      .build()

    const futurePricingParameters1 = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod1)
      .withBaseCharge(new Euros(15))
      .withExtraMinutesPerSeatTaken(20)
      .withExtraMinutesPerWheelchair(35)
      .build()

    const futurePricingParameters2 = new PricingParametersEntityBuilder()
      .withPricingFormulaUuid(pricingFormula.uuid)
      .withEffectivePeriod(futureEffectivePeriod2)
      .withBaseCharge(new Euros(20))
      .withExtraMinutesPerSeatTaken(25)
      .withExtraMinutesPerWheelchair(40)
      .build()

    await testSetup.dataSource.manager.insert(PricingParameters, [
      currentPricingParameters,
      pastPricingParameters1,
      pastPricingParameters2,
      futurePricingParameters1,
      futurePricingParameters2
    ])

    const query = new ViewPricingParametersByFormulaQueryBuilder()
      .withLimit(10)
      .withOffset(0)
      .build()

    const response = await request(testSetup.httpServer)
      .get(`/pricing-formulas/${pricingFormula.uuid}/pricing-parameters/ranges`)
      .set('Authorization', `Bearer ${adminUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
    expect(response.body.items).toHaveLength(5)
    expect(response.body.items[0]).toEqual({
      uuid: futurePricingParameters2.uuid,
      pricingFormulaUuid: pricingFormula.uuid,
      effectivePeriod: {
        startDate: futureEffectivePeriod2.startDate.toString(),
        endDate: futureEffectivePeriod2.endDate.toString()
      },
      period: DatePeriod.FUTURE
    })
    expect(response.body.items[1]).toEqual({
      uuid: futurePricingParameters1.uuid,
      pricingFormulaUuid: pricingFormula.uuid,
      effectivePeriod: {
        startDate: futureEffectivePeriod1.startDate.toString(),
        endDate: futureEffectivePeriod1.endDate.toString()
      },
      period: DatePeriod.FUTURE
    })
    expect(response.body.items[2]).toEqual({
      uuid: currentPricingParameters.uuid,
      pricingFormulaUuid: pricingFormula.uuid,
      effectivePeriod: {
        startDate: currentEffectivePeriod.startDate.toString(),
        endDate: currentEffectivePeriod.endDate.toString()
      },
      period: DatePeriod.ACTIVE
    })
    expect(response.body.items[3]).toEqual({
      uuid: pastPricingParameters2.uuid,
      pricingFormulaUuid: pricingFormula.uuid,
      effectivePeriod: {
        startDate: pastEffectivePeriod2.startDate.toString(),
        endDate: pastEffectivePeriod2.endDate.toString()
      },
      period: DatePeriod.PAST
    })
    expect(response.body.items[4]).toEqual({
      uuid: pastPricingParameters1.uuid,
      pricingFormulaUuid: pricingFormula.uuid,
      effectivePeriod: {
        startDate: pastEffectivePeriod1.startDate.toString(),
        endDate: pastEffectivePeriod1.endDate.toString()
      },
      period: DatePeriod.PAST
    })
    expect(response.body.meta).toEqual({
      total: 5,
      limit: 10,
      offset: 0
    })
  })
})
