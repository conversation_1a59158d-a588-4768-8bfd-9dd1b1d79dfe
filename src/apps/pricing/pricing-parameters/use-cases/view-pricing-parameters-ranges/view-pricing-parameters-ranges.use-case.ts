import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { typeormPagination } from '@wisemen/pagination'
import { Repository } from 'typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { ViewPricingParametersByFormulaQuery } from './view-pricing-parameters-ranges.query.js'
import { ViewPricingParametersByFormulaResponse } from './view-pricing-parameters-ranges.response.js'

@Injectable()
export class ViewPricingParametersByFormulaUseCase {
  constructor (
    @InjectRepository(PricingParameters)
    private readonly pricingParametersRepository: Repository<PricingParameters>
  ) {}

  async execute (
    pricingFormulaUuid: string,
    query: ViewPricingParametersByFormulaQuery = new ViewPricingParametersByFormulaQuery()
  ): Promise<ViewPricingParametersByFormulaResponse> {
    const pagination = typeormPagination(query.pagination)

    const [pricingParameters, count]
      = await this.getPricingParameters(pricingFormulaUuid, pagination)

    return new ViewPricingParametersByFormulaResponse(
      pricingParameters,
      count,
      pagination.take,
      pagination.skip
    )
  }

  private async getPricingParameters (
    pricingFormulaUuid: string,
    pagination: { skip: number, take: number }
  ): Promise<[PricingParameters[], number]> {
    return this.pricingParametersRepository
      .createQueryBuilder('pricingParameters')
      .where('pricingParameters.pricingFormulaUuid = :pricingFormulaUuid', { pricingFormulaUuid })
      .orderBy('lower(pricingParameters.effective_period)', 'DESC')
      .skip(pagination.skip)
      .take(pagination.take)
      .getManyAndCount()
  }
}
