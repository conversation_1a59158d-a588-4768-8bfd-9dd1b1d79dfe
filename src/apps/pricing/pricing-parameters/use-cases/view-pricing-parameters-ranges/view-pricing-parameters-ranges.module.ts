import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PricingParameters } from '../../pricing-parameters.entity.js'
import { ViewPricingParametersRangesController } from './view-pricing-parameters-ranges.controller.js'
import { ViewPricingParametersByFormulaUseCase } from './view-pricing-parameters-ranges.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PricingParameters])
  ],
  controllers: [ViewPricingParametersRangesController],
  providers: [ViewPricingParametersByFormulaUseCase]
})
export class ViewPricingParametersByFormulaModule {}
