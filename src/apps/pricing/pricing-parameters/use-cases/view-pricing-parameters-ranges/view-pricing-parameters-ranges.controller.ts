import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ViewPricingParametersByFormulaUseCase } from './view-pricing-parameters-ranges.use-case.js'
import { ViewPricingParametersByFormulaResponse } from './view-pricing-parameters-ranges.response.js'
import { ViewPricingParametersByFormulaQuery } from './view-pricing-parameters-ranges.query.js'

@ApiTags('Pricing')
@ApiOAuth2([])
@Controller('pricing-formulas/:pricingFormulaUuid/pricing-parameters/ranges')
export class ViewPricingParametersRangesController {
  constructor (
    private readonly viewPricingParametersByFormulaUseCase: ViewPricingParametersByFormulaUseCase
  ) {}

  @Get()
  @ApiOkResponse({
    description: 'Returns all pricing parameters for a pricing formula ordered',
    type: ViewPricingParametersByFormulaResponse
  })
  async execute (
    @UuidParam('pricingFormulaUuid') pricingFormulaUuid: string,
    @Query() query: ViewPricingParametersByFormulaQuery
  ): Promise<ViewPricingParametersByFormulaResponse> {
    return await this.viewPricingParametersByFormulaUseCase.execute(pricingFormulaUuid, query)
  }
}
