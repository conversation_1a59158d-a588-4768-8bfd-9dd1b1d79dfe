import { randomUUID } from 'crypto'
import { Euros } from '../../../utils/monetary/euros.js'
import { DateRange } from '../../../utils/dates/date-ranges/date-range.js'
import { PastInfinityDate } from '../../../utils/dates/wise-date/past-infinity-date.js'
import { FutureInfinityDate } from '../../../utils/dates/wise-date/future-infinity-date.js'
import { PricingParameters } from './pricing-parameters.entity.js'

export class PricingParametersEntityBuilder {
  private parameters: PricingParameters

  constructor () {
    this.parameters = new PricingParameters()
    this.parameters.uuid = randomUUID()
    this.parameters.createdAt = new Date()
    this.parameters.updatedAt = new Date()
    this.parameters.deletedAt = null
    this.parameters.pricingFormulaUuid = randomUUID()
    this.parameters.effectivePeriod = new DateRange(
      new PastInfinityDate(),
      new FutureInfinityDate()
    )
    this.parameters.baseCharge = null
    this.parameters.extraMinutesPerSeatTaken = null
    this.parameters.extraMinutesPerWheelchair = null
  }

  withUuid (uuid: string): this {
    this.parameters.uuid = uuid
    return this
  }

  withCreatedAt (date: Date): this {
    this.parameters.createdAt = date
    return this
  }

  withUpdatedAt (date: Date): this {
    this.parameters.updatedAt = date
    return this
  }

  withDeletedAt (date: Date | null): this {
    this.parameters.deletedAt = date
    return this
  }

  withPricingFormulaUuid (uuid: string): this {
    this.parameters.pricingFormulaUuid = uuid
    return this
  }

  withEffectivePeriod (period: DateRange): this {
    this.parameters.effectivePeriod = period
    return this
  }

  withBaseCharge (amount: Euros | null): this {
    this.parameters.baseCharge = amount
    return this
  }

  withExtraMinutesPerSeatTaken (minutes: number | null): this {
    this.parameters.extraMinutesPerSeatTaken = minutes
    return this
  }

  withExtraMinutesPerWheelchair (minutes: number | null): this {
    this.parameters.extraMinutesPerWheelchair = minutes
    return this
  }

  copy (pricingParameters: PricingParameters): this {
    return this.withUuid(pricingParameters.uuid)
      .withCreatedAt(pricingParameters.createdAt)
      .withUpdatedAt(pricingParameters.updatedAt)
      .withDeletedAt(pricingParameters.deletedAt)
      .withPricingFormulaUuid(pricingParameters.pricingFormulaUuid)
      .withEffectivePeriod(pricingParameters.effectivePeriod)
      .withBaseCharge(pricingParameters.baseCharge)
      .withExtraMinutesPerSeatTaken(pricingParameters.extraMinutesPerSeatTaken)
      .withExtraMinutesPerWheelchair(pricingParameters.extraMinutesPerWheelchair)
  }

  build (): PricingParameters {
    return this.parameters
  }
}
