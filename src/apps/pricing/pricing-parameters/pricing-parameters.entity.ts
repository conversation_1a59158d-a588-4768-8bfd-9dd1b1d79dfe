import { Column, CreateDateColumn, DeleteDateColumn, Entity, Exclusion, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm'
import { DateRange } from '../../../utils/dates/date-ranges/date-range.js'
import { DateRangeColumn } from '../../../utils/typeorm/columns/date-range-column.js'
import { PricingFormula } from '../pricing-formula/entities/pricing-formula.entity.js'
import { EurosColumn, Euros } from '../../../utils/monetary/euros.js'

@Entity()
export class PricingParameters {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date

  @DeleteDateColumn({ type: 'timestamptz' })
  deletedAt: Date | null

  @DateRangeColumn()
  @Exclusion('USING GIST (pricing_formula_uuid WITH =, effective_period WITH &&) WHERE (deleted_at IS NULL)')
  effectivePeriod: DateRange

  @Column({ type: 'uuid' })
  pricingFormulaUuid: string

  @ManyToOne(() => PricingFormula)
  @JoinColumn({ name: 'pricing_formula_uuid' })
  pricingFormula?: Relation<PricingFormula>

  @EurosColumn({ nullable: true })
  baseCharge: Euros | null

  @Column({ type: 'float', nullable: true })
  extraMinutesPerSeatTaken: number | null

  @Column({ type: 'float', nullable: true })
  extraMinutesPerWheelchair: number | null
}
